import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:twitter_video_downloader/main.dart';
import 'package:twitter_video_downloader/services/download_manager.dart';

void main() {
  testWidgets('App should start without errors', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp());

    // Verify that the app starts with the correct title
    expect(find.text('تحميل فيديوهات تويتر'), findsOneWidget);
    
    // Verify that both tabs are present
    expect(find.text('المتصفح'), findsOneWidget);
    expect(find.text('التحميلات'), findsOneWidget);
  });

  testWidgets('Downloads tab shows empty state initially', (WidgetTester tester) async {
    await tester.pumpWidget(MyApp());

    // Tap on the downloads tab
    await tester.tap(find.text('التحميلات'));
    await tester.pumpAndSettle();

    // Verify empty state is shown
    expect(find.text('لا توجد تحميلات بعد'), findsOneWidget);
    expect(find.text('استخدم المتصفح لتحميل الفيديوهات من تويتر'), findsOneWidget);
  });

  group('DownloadManager Tests', () {
    test('should initialize with empty downloads list', () {
      final downloadManager = DownloadManager();
      expect(downloadManager.downloads, isEmpty);
    });

    test('should add download item when downloadVideo is called', () async {
      final downloadManager = DownloadManager();
      
      // Note: This test would need to be modified to work with actual network calls
      // For now, we're just testing the structure
      expect(downloadManager.downloads, isEmpty);
    });
  });

  group('TwitterExtractor Tests', () {
    test('should detect Twitter URLs correctly', () {
      // These tests would verify the URL detection logic
      // Implementation depends on the actual TwitterExtractor methods
    });
  });
}
