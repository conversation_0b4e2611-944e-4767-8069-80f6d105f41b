import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../services/download_manager.dart';

class DownloadItemWidget extends StatelessWidget {
  final DownloadItem download;

  const DownloadItemWidget({Key? key, required this.download}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildStatusIcon(),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        download.fileName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStatusText(),
                        style: TextStyle(
                          color: _getStatusColor(),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildActionButton(context),
              ],
            ),
            const SizedBox(height: 12),
            _buildProgressBar(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDateTime(download.startTime),
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
                Text(
                  _getProgressText(),
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            if (download.error != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red[600], size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        download.error!,
                        style: TextStyle(
                          color: Colors.red[600],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    switch (download.status) {
      case DownloadStatus.downloading:
        return const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case DownloadStatus.completed:
        return Icon(Icons.check_circle, color: Colors.green[600], size: 24);
      case DownloadStatus.failed:
        return Icon(Icons.error, color: Colors.red[600], size: 24);
      case DownloadStatus.paused:
        return Icon(Icons.pause_circle, color: Colors.orange[600], size: 24);
    }
  }

  Widget _buildActionButton(BuildContext context) {
    switch (download.status) {
      case DownloadStatus.downloading:
        return IconButton(
          onPressed: () {
            // إيقاف التحميل مؤقتاً (يمكن تنفيذه لاحقاً)
          },
          icon: const Icon(Icons.pause),
          tooltip: 'إيقاف مؤقت',
        );
      case DownloadStatus.completed:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _openFile(context),
              icon: const Icon(Icons.play_arrow),
              tooltip: 'تشغيل',
            ),
            IconButton(
              onPressed: () => _shareFile(context),
              icon: const Icon(Icons.share),
              tooltip: 'مشاركة',
            ),
          ],
        );
      case DownloadStatus.failed:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _retryDownload(context),
              icon: const Icon(Icons.refresh),
              tooltip: 'إعادة المحاولة',
            ),
            IconButton(
              onPressed: () => _deleteDownload(context),
              icon: const Icon(Icons.delete),
              tooltip: 'حذف',
            ),
          ],
        );
      case DownloadStatus.paused:
        return IconButton(
          onPressed: () => _retryDownload(context),
          icon: const Icon(Icons.play_arrow),
          tooltip: 'استكمال',
        );
    }
  }

  Widget _buildProgressBar() {
    if (download.status == DownloadStatus.downloading) {
      return LinearProgressIndicator(
        value: download.progress,
        backgroundColor: Colors.grey[300],
        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
      );
    } else if (download.status == DownloadStatus.completed) {
      return LinearProgressIndicator(
        value: 1.0,
        backgroundColor: Colors.grey[300],
        valueColor: AlwaysStoppedAnimation<Color>(Colors.green[600]!),
      );
    } else {
      return LinearProgressIndicator(
        value: download.progress,
        backgroundColor: Colors.grey[300],
        valueColor: AlwaysStoppedAnimation<Color>(Colors.red[600]!),
      );
    }
  }

  String _getStatusText() {
    switch (download.status) {
      case DownloadStatus.downloading:
        return 'جاري التحميل...';
      case DownloadStatus.completed:
        return 'تم التحميل بنجاح';
      case DownloadStatus.failed:
        return 'فشل التحميل';
      case DownloadStatus.paused:
        return 'متوقف مؤقتاً';
    }
  }

  Color _getStatusColor() {
    switch (download.status) {
      case DownloadStatus.downloading:
        return Colors.blue[600]!;
      case DownloadStatus.completed:
        return Colors.green[600]!;
      case DownloadStatus.failed:
        return Colors.red[600]!;
      case DownloadStatus.paused:
        return Colors.orange[600]!;
    }
  }

  String _getProgressText() {
    if (download.status == DownloadStatus.completed) {
      return '100%';
    } else {
      return '${(download.progress * 100).toInt()}%';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _openFile(BuildContext context) {
    // فتح الملف باستخدام التطبيق الافتراضي
    // يمكن استخدام مكتبة open_file أو url_launcher
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح الملف...')),
    );
  }

  void _shareFile(BuildContext context) {
    // مشاركة الملف
    // يمكن استخدام مكتبة share_plus
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة الملف...')),
    );
  }

  void _retryDownload(BuildContext context) {
    final downloadManager = Provider.of<DownloadManager>(context, listen: false);
    downloadManager.retryDownload(download.id);
  }

  void _deleteDownload(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('حذف التحميل'),
          content: const Text('هل أنت متأكد من حذف هذا التحميل؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                final downloadManager = Provider.of<DownloadManager>(context, listen: false);
                downloadManager.removeDownload(download.id);
                
                // حذف الملف من التخزين
                final file = File(download.filePath);
                if (file.existsSync()) {
                  file.deleteSync();
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
