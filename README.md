# تطبيق تحميل فيديوهات تويتر

تطبيق Flutter لتحميل الفيديوهات من تويتر مع متصفح داخلي مدمج.

## الميزات

### 🌐 متصفح داخلي
- تصفح تويتر مباشرة داخل التطبيق
- أزرار التنقل (للخلف، للأمام، إعادة تحميل)
- عرض الرابط الحالي
- كشف تلقائي للفيديوهات في الصفحات

### 📥 تحميل الفيديوهات
- تحميل الفيديوهات بجودة عالية
- عرض تقدم التحميل في الوقت الفعلي
- حفظ الملفات في مجلد التحميل
- إدارة قائمة التحميلات

### 📱 واجهة مستخدم عربية
- دعم كامل للغة العربية
- تصميم حديث ومتجاوب
- أيقونات واضحة ومفهومة
- رسائل خطأ مفيدة

## التبعيات المستخدمة

```yaml
dependencies:
  flutter: sdk
  webview_flutter: ^4.4.2    # المتصفح الداخلي
  http: ^1.1.0               # طلبات الشبكة
  dio: ^5.3.2                # تحميل الملفات
  path_provider: ^2.1.1      # إدارة المسارات
  permission_handler: ^11.0.1 # الصلاحيات
  url_launcher: ^6.2.1       # فتح الروابط
  provider: ^6.1.1           # إدارة الحالة
```

## هيكل المشروع

```
lib/
├── main.dart                    # نقطة البداية
├── screens/
│   ├── home_screen.dart         # الشاشة الرئيسية
│   └── browser_screen.dart      # شاشة المتصفح
├── services/
│   ├── download_manager.dart    # إدارة التحميلات
│   └── twitter_extractor.dart   # استخراج روابط الفيديو
└── widgets/
    └── download_item.dart       # عنصر عرض التحميل
```

## كيفية الاستخدام

### 1. تصفح تويتر
- افتح التطبيق
- انتقل إلى تبويب "المتصفح"
- تصفح تويتر والبحث عن الفيديوهات

### 2. تحميل الفيديوهات
- عند العثور على فيديو، سيظهر زر التحميل
- اضغط على زر التحميل أو انتظر النافذة المنبثقة
- اختر "تحميل" لبدء العملية

### 3. إدارة التحميلات
- انتقل إلى تبويب "التحميلات"
- شاهد تقدم التحميلات الجارية
- قم بتشغيل أو مشاركة الفيديوهات المكتملة

## الصلاحيات المطلوبة

### Android
- `INTERNET`: للوصول إلى الإنترنت
- `WRITE_EXTERNAL_STORAGE`: لحفظ الملفات
- `READ_EXTERNAL_STORAGE`: لقراءة الملفات
- `ACCESS_NETWORK_STATE`: لفحص حالة الشبكة

### iOS
- لا توجد صلاحيات خاصة مطلوبة

## التشغيل والتطوير

### متطلبات النظام
- Flutter SDK 3.10.0 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- Android Studio أو VS Code
- جهاز Android أو iOS للاختبار

### خطوات التشغيل

1. **تثبيت التبعيات:**
```bash
flutter pub get
```

2. **تشغيل التطبيق:**
```bash
flutter run
```

3. **بناء التطبيق للإنتاج:**
```bash
# Android
flutter build apk --release

# iOS
flutter build ios --release
```

## ملاحظات مهمة

### استخراج الفيديوهات
- يستخدم التطبيق طرق متعددة لاستخراج روابط الفيديو
- في حالة عدم توفر API، يتم استخدام رابط تجريبي
- يمكن تحسين الاستخراج باستخدام خدمات خارجية

### الأمان والخصوصية
- التطبيق لا يحفظ بيانات المستخدم
- جميع التحميلات محلية على الجهاز
- لا يتم إرسال أي بيانات لخوادم خارجية

### التحسينات المستقبلية
- دعم منصات أخرى (YouTube, Instagram)
- تحميل متعدد الخيوط
- ضغط الفيديوهات
- مشاركة مباشرة على وسائل التواصل

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

في حالة وجود مشاكل أو استفسارات:
- افتح Issue جديد في GitHub
- تأكد من تضمين تفاصيل الخطأ
- اذكر نوع الجهاز ونسخة النظام
