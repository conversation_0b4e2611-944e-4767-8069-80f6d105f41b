import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

class DownloadItem {
  final String id;
  final String url;
  final String fileName;
  final String filePath;
  final DateTime startTime;
  double progress;
  DownloadStatus status;
  String? error;

  DownloadItem({
    required this.id,
    required this.url,
    required this.fileName,
    required this.filePath,
    required this.startTime,
    this.progress = 0.0,
    this.status = DownloadStatus.downloading,
    this.error,
  });
}

enum DownloadStatus {
  downloading,
  completed,
  failed,
  paused,
}

class DownloadManager extends ChangeNotifier {
  final List<DownloadItem> _downloads = [];
  final Dio _dio = Dio();

  List<DownloadItem> get downloads => List.unmodifiable(_downloads);

  Future<void> downloadVideo(String videoUrl, String originalUrl) async {
    // طلب الصلاحيات
    if (!await _requestPermissions()) {
      throw Exception('الصلاحيات مطلوبة لحفظ الملفات');
    }

    // إنشاء معرف فريد للتحميل
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final fileName = 'twitter_video_$id.mp4';
    
    // الحصول على مجلد التحميل
    final directory = await _getDownloadDirectory();
    final filePath = '${directory.path}/$fileName';

    // إنشاء عنصر التحميل
    final downloadItem = DownloadItem(
      id: id,
      url: videoUrl,
      fileName: fileName,
      filePath: filePath,
      startTime: DateTime.now(),
    );

    _downloads.insert(0, downloadItem);
    notifyListeners();

    try {
      // بدء التحميل
      await _dio.download(
        videoUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            downloadItem.progress = received / total;
            notifyListeners();
          }
        },
      );

      downloadItem.status = DownloadStatus.completed;
      downloadItem.progress = 1.0;
    } catch (e) {
      downloadItem.status = DownloadStatus.failed;
      downloadItem.error = e.toString();
    }

    notifyListeners();
  }

  Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    return true; // iOS لا يحتاج صلاحيات خاصة للكتابة في مجلد التطبيق
  }

  Future<Directory> _getDownloadDirectory() async {
    if (Platform.isAndroid) {
      // محاولة الحصول على مجلد التحميل العام
      try {
        final directory = Directory('/storage/emulated/0/Download/TwitterVideos');
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }
        return directory;
      } catch (e) {
        // في حالة الفشل، استخدم مجلد التطبيق
        final directory = await getApplicationDocumentsDirectory();
        final downloadDir = Directory('${directory.path}/Downloads');
        if (!await downloadDir.exists()) {
          await downloadDir.create(recursive: true);
        }
        return downloadDir;
      }
    } else {
      // iOS
      final directory = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${directory.path}/Downloads');
      if (!await downloadDir.exists()) {
        await downloadDir.create(recursive: true);
      }
      return downloadDir;
    }
  }

  void removeDownload(String id) {
    _downloads.removeWhere((download) => download.id == id);
    notifyListeners();
  }

  void retryDownload(String id) async {
    final download = _downloads.firstWhere((d) => d.id == id);
    download.status = DownloadStatus.downloading;
    download.progress = 0.0;
    download.error = null;
    notifyListeners();

    try {
      await _dio.download(
        download.url,
        download.filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            download.progress = received / total;
            notifyListeners();
          }
        },
      );

      download.status = DownloadStatus.completed;
      download.progress = 1.0;
    } catch (e) {
      download.status = DownloadStatus.failed;
      download.error = e.toString();
    }

    notifyListeners();
  }
}
