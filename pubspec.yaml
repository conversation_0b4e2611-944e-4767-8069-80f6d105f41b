name: twitter_video_downloader
description: تطبيق لتحميل الفيديوهات من تويتر مع متصفح داخلي

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI
  cupertino_icons: ^1.0.2
  
  # Web browsing
  webview_flutter: ^4.4.2
  
  # Network
  http: ^1.1.0
  dio: ^5.3.2
  
  # File handling
  path_provider: ^2.1.1
  
  # Permissions
  permission_handler: ^11.0.1
  
  # URL handling
  url_launcher: ^6.2.1
  
  # State management
  provider: ^6.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
