import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'browser_screen.dart';
import '../services/download_manager.dart';
import '../widgets/download_item.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تحميل فيديوهات تويتر'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.web),
              text: 'المتصفح',
            ),
            Tab(
              icon: Icon(Icons.download),
              text: 'التحميلات',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          BrowserScreen(),
          _buildDownloadsTab(),
        ],
      ),
    );
  }

  Widget _buildDownloadsTab() {
    return Consumer<DownloadManager>(
      builder: (context, downloadManager, child) {
        if (downloadManager.downloads.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.download_outlined,
                  size: 80,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد تحميلات بعد',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'استخدم المتصفح لتحميل الفيديوهات من تويتر',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: downloadManager.downloads.length,
          itemBuilder: (context, index) {
            final download = downloadManager.downloads[index];
            return DownloadItemWidget(download: download);
          },
        );
      },
    );
  }
}
