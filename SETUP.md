# دليل إعداد وتشغيل التطبيق

## متطلبات النظام

### 1. تثبيت Flutter
```bash
# تحميل Flutter SDK من الموقع الرسمي
https://flutter.dev/docs/get-started/install

# إضافة Flutter إلى PATH
export PATH="$PATH:`pwd`/flutter/bin"

# التحقق من التثبيت
flutter doctor
```

### 2. إعداد Android Studio
- تحميل وتثبيت Android Studio
- تثبيت Android SDK
- إنشاء Android Virtual Device (AVD)

### 3. إعداد الجهاز
- تفعيل وضع المطور على الجهاز
- تفعيل USB Debugging
- أو استخدام محاكي Android

## خطوات التشغيل

### 1. تحضير المشروع
```bash
# الانتقال إلى مجلد المشروع
cd twitter_video_downloader

# تثبيت التبعيات
flutter pub get

# التحقق من عدم وجود مشاكل
flutter doctor
```

### 2. تشغيل التطبيق
```bash
# عرض الأجهزة المتاحة
flutter devices

# تشغيل التطبيق على الجهاز المتصل
flutter run

# أو تشغيل في وضع التطوير مع Hot Reload
flutter run --debug
```

### 3. بناء التطبيق للإنتاج
```bash
# بناء APK للأندرويد
flutter build apk --release

# بناء App Bundle (مستحسن للنشر)
flutter build appbundle --release

# العثور على الملف المبني
# APK: build/app/outputs/flutter-apk/app-release.apk
# Bundle: build/app/outputs/bundle/release/app-release.aab
```

## حل المشاكل الشائعة

### 1. مشكلة Gradle
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# إعادة بناء المشروع
flutter build apk
```

### 2. مشكلة الصلاحيات
- تأكد من إضافة الصلاحيات في AndroidManifest.xml
- اطلب الصلاحيات في وقت التشغيل

### 3. مشكلة WebView
```bash
# تأكد من إضافة internet permission
<uses-permission android:name="android.permission.INTERNET" />

# تفعيل cleartext traffic
android:usesCleartextTraffic="true"
```

### 4. مشكلة التحميل
- تحقق من صلاحيات التخزين
- تأكد من وجود مساحة كافية
- فحص اتصال الإنترنت

## اختبار التطبيق

### 1. اختبار المتصفح
- فتح twitter.com
- التنقل بين الصفحات
- البحث عن فيديوهات

### 2. اختبار التحميل
- العثور على تغريدة تحتوي على فيديو
- الضغط على زر التحميل
- مراقبة تقدم التحميل

### 3. اختبار إدارة الملفات
- عرض قائمة التحميلات
- تشغيل الفيديوهات
- حذف الملفات

## تخصيص التطبيق

### 1. تغيير الألوان
```dart
// في main.dart
theme: ThemeData(
  primarySwatch: Colors.blue, // غير اللون هنا
  // ...
),
```

### 2. إضافة خطوط عربية
- إضافة ملفات الخطوط في assets/fonts/
- تحديث pubspec.yaml
- استخدام الخط في التطبيق

### 3. تحسين استخراج الفيديو
- استخدام API خارجي
- تحسين regex patterns
- إضافة دعم لمنصات أخرى

## نشر التطبيق

### 1. Google Play Store
- إنشاء حساب مطور
- إعداد App Bundle
- رفع التطبيق ومراجعته

### 2. توزيع مباشر
- بناء APK
- توقيع التطبيق
- توزيع الملف

## الدعم والمساعدة

### موارد مفيدة
- [Flutter Documentation](https://flutter.dev/docs)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)
- [Android Developer Guide](https://developer.android.com/guide)

### المجتمع
- [Flutter Community](https://flutter.dev/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)
- [GitHub Issues](https://github.com/flutter/flutter/issues)

## ملاحظات أمنية

### 1. حماية البيانات
- عدم حفظ كلمات المرور
- تشفير البيانات الحساسة
- استخدام HTTPS فقط

### 2. الصلاحيات
- طلب الصلاحيات عند الحاجة فقط
- شرح سبب الحاجة للمستخدم
- احترام خصوصية المستخدم

### 3. التحديثات
- مراقبة الثغرات الأمنية
- تحديث التبعيات بانتظام
- اختبار التحديثات قبل النشر
