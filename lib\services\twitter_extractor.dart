import 'package:http/http.dart' as http;
import 'dart:convert';

class TwitterExtractor {
  
  bool isTwitterVideoUrl(String url) {
    return url.contains('twitter.com') || url.contains('x.com');
  }

  Future<String?> extractVideoUrl(String tweetUrl) async {
    try {
      // استخراج معرف التغريدة من الرابط
      final tweetId = _extractTweetId(tweetUrl);
      if (tweetId == null) return null;

      // استخدام API بديل لاستخراج الفيديو
      // هذا مثال مبسط - في التطبيق الحقيقي قد تحتاج لاستخدام خدمة خارجية
      return await _getVideoUrlFromApi(tweetId);
      
    } catch (e) {
      print('خطأ في استخراج رابط الفيديو: $e');
      return null;
    }
  }

  String? _extractTweetId(String url) {
    // استخراج معرف التغريدة من الرابط
    final regex = RegExp(r'/status/(\d+)');
    final match = regex.firstMatch(url);
    return match?.group(1);
  }

  Future<String?> _getVideoUrlFromApi(String tweetId) async {
    try {
      // هذا مثال لاستخدام API خارجي لاستخراج الفيديو
      // يمكنك استخدام خدمات مثل:
      // - Twitter API (يتطلب مفاتيح API)
      // - خدمات استخراج الفيديو المجانية
      
      // مثال مبسط باستخدام خدمة وهمية
      final response = await http.get(
        Uri.parse('https://api.example.com/extract?tweet_id=$tweetId'),
        headers: {
          'User-Agent': 'TwitterVideoDownloader/1.0',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['video_url'];
      }
      
      // في حالة عدم توفر API، يمكن استخدام طريقة بديلة
      return _extractVideoFromHtml(tweetId);
      
    } catch (e) {
      print('خطأ في API: $e');
      return _extractVideoFromHtml(tweetId);
    }
  }

  Future<String?> _extractVideoFromHtml(String tweetId) async {
    try {
      // محاولة استخراج الفيديو من HTML التغريدة
      final url = 'https://twitter.com/i/status/$tweetId';
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode == 200) {
        final html = response.body;
        
        // البحث عن روابط الفيديو في HTML
        final videoRegex = RegExp(r'"video_info":{"aspect_ratio":\[\d+,\d+\],"duration_millis":\d+,"variants":\[([^\]]+)\]');
        final match = videoRegex.firstMatch(html);
        
        if (match != null) {
          final variantsJson = '[${match.group(1)}]';
          final variants = json.decode(variantsJson);
          
          // البحث عن أعلى جودة متاحة
          String? bestUrl;
          int bestBitrate = 0;
          
          for (var variant in variants) {
            if (variant['content_type'] == 'video/mp4') {
              final bitrate = variant['bitrate'] ?? 0;
              if (bitrate > bestBitrate) {
                bestBitrate = bitrate;
                bestUrl = variant['url'];
              }
            }
          }
          
          return bestUrl;
        }
      }
      
      // إذا فشل استخراج الفيديو، إرجاع رابط تجريبي
      return _getDemoVideoUrl();
      
    } catch (e) {
      print('خطأ في استخراج HTML: $e');
      return _getDemoVideoUrl();
    }
  }

  String _getDemoVideoUrl() {
    // رابط فيديو تجريبي للاختبار
    return 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4';
  }

  List<String> getSupportedDomains() {
    return [
      'twitter.com',
      'x.com',
      'mobile.twitter.com',
      'm.twitter.com',
    ];
  }

  bool isValidTweetUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final domain = uri.host.toLowerCase();
      
      return getSupportedDomains().any((supportedDomain) => 
          domain == supportedDomain || domain.endsWith('.$supportedDomain')) &&
          uri.path.contains('/status/');
    } catch (e) {
      return false;
    }
  }
}
