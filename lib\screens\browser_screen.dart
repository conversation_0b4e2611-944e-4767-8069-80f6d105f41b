import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:provider/provider.dart';
import '../services/download_manager.dart';
import '../services/twitter_extractor.dart';

class BrowserScreen extends StatefulWidget {
  @override
  _BrowserScreenState createState() => _BrowserScreenState();
}

class _BrowserScreenState extends State<BrowserScreen> {
  late WebViewController _controller;
  bool _isLoading = true;
  String _currentUrl = '';
  final TwitterExtractor _extractor = TwitterExtractor();

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _currentUrl = url;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
              _currentUrl = url;
            });
            _checkForTwitterVideo(url);
          },
        ),
      )
      ..loadRequest(Uri.parse('https://twitter.com'));
  }

  void _checkForTwitterVideo(String url) {
    if (_extractor.isTwitterVideoUrl(url)) {
      _showDownloadDialog(url);
    }
  }

  void _showDownloadDialog(String url) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تحميل فيديو'),
          content: const Text('تم العثور على فيديو في هذه الصفحة. هل تريد تحميله؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _downloadVideo(url);
              },
              child: const Text('تحميل'),
            ),
          ],
        );
      },
    );
  }

  void _downloadVideo(String url) async {
    final downloadManager = Provider.of<DownloadManager>(context, listen: false);
    
    try {
      final videoUrl = await _extractor.extractVideoUrl(url);
      if (videoUrl != null) {
        await downloadManager.downloadVideo(videoUrl, url);
        _showSnackBar('بدأ التحميل...');
      } else {
        _showSnackBar('لم يتم العثور على رابط الفيديو');
      }
    } catch (e) {
      _showSnackBar('خطأ في التحميل: ${e.toString()}');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          color: Colors.grey[100],
          child: Row(
            children: [
              IconButton(
                onPressed: () => _controller.goBack(),
                icon: const Icon(Icons.arrow_back),
              ),
              IconButton(
                onPressed: () => _controller.goForward(),
                icon: const Icon(Icons.arrow_forward),
              ),
              IconButton(
                onPressed: () => _controller.reload(),
                icon: const Icon(Icons.refresh),
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Text(
                    _currentUrl.isEmpty ? 'twitter.com' : _currentUrl,
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              if (_extractor.isTwitterVideoUrl(_currentUrl))
                IconButton(
                  onPressed: () => _downloadVideo(_currentUrl),
                  icon: const Icon(Icons.download, color: Colors.blue),
                ),
            ],
          ),
        ),
        if (_isLoading)
          const LinearProgressIndicator(),
        Expanded(
          child: WebViewWidget(controller: _controller),
        ),
      ],
    );
  }
}
